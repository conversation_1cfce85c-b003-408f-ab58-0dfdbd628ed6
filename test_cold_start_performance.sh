#!/bin/bash

# Cold Start Performance Testing Script
# For TJ_BatteryOne Android Application
# macOS/Linux compatible version

PACKAGE_NAME="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
ADB_PATH="adb"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if ADB is available and device is connected
check_adb_setup() {
    echo -e "${BLUE}🔍 Checking ADB setup...${NC}"
    
    if ! command -v "$ADB_PATH" &> /dev/null; then
        echo -e "${RED}❌ ADB not found. Please install Android SDK platform-tools${NC}"
        exit 1
    fi
    
    local devices=$("$ADB_PATH" devices | grep -v "List of devices" | grep -v "^$")
    if [ -z "$devices" ]; then
        echo -e "${RED}❌ No Android devices connected${NC}"
        echo "Please connect your device and enable USB debugging"
        exit 1
    fi
    
    echo -e "${GREEN}✅ ADB setup OK${NC}"
    echo "Connected devices:"
    "$ADB_PATH" devices
}

# Function to measure cold start performance
measure_cold_start() {
    local test_name="$1"
    local activity="$2"
    local description="$3"
    
    echo ""
    echo -e "${BLUE}📊 Testing: $description${NC}"
    echo "Activity: $activity"
    
    # Force stop and clear cache
    echo "🛑 Force stopping app..."
    "$ADB_PATH" shell am force-stop "$PACKAGE_NAME"
    sleep 3
    
    # Clear logcat buffer
    "$ADB_PATH" logcat -c
    
    # Start activity and measure time
    echo "🚀 Starting app..."
    local start_time=$(python3 -c "import time; print(int(time.time() * 1000))" 2>/dev/null || echo $(($(date +%s) * 1000)))
    "$ADB_PATH" shell am start -W -n "$PACKAGE_NAME/$activity" > "/tmp/${test_name}_startup.txt" 2>&1
    local end_time=$(python3 -c "import time; print(int(time.time() * 1000))" 2>/dev/null || echo $(($(date +%s) * 1000)))

    local total_time=$((end_time - start_time))
    
    # Extract ADB timing details
    local this_time=""
    local total_time_adb=""
    local wait_time=""
    
    if [ -f "/tmp/${test_name}_startup.txt" ]; then
        this_time=$(grep "ThisTime:" "/tmp/${test_name}_startup.txt" | awk '{print $2}')
        total_time_adb=$(grep "TotalTime:" "/tmp/${test_name}_startup.txt" | awk '{print $2}')
        wait_time=$(grep "WaitTime:" "/tmp/${test_name}_startup.txt" | awk '{print $2}')
    fi
    
    # Display results
    echo -e "${GREEN}⏱️  Results:${NC}"
    echo "   Script measurement: ${total_time}ms"
    [ -n "$this_time" ] && echo "   ADB ThisTime: ${this_time}ms"
    [ -n "$total_time_adb" ] && echo "   ADB TotalTime: ${total_time_adb}ms"
    [ -n "$wait_time" ] && echo "   ADB WaitTime: ${wait_time}ms"
    
    # Capture startup logs
    sleep 2
    echo "📝 Capturing startup logs..."
    "$ADB_PATH" logcat -d | grep -E "STARTUP_TIMING|COLD_START" > "/tmp/${test_name}_logs.txt"
    
    local log_count=$(wc -l < "/tmp/${test_name}_logs.txt" 2>/dev/null || echo "0")
    echo "   Captured $log_count startup log entries"
    
    # Performance assessment
    if [ -n "$total_time_adb" ] && [ "$total_time_adb" -gt 1000 ]; then
        echo -e "${RED}⚠️  Startup time > 1000ms - optimization needed${NC}"
    elif [ -n "$total_time_adb" ] && [ "$total_time_adb" -gt 600 ]; then
        echo -e "${YELLOW}⚡ Startup time acceptable but can be improved${NC}"
    else
        echo -e "${GREEN}✅ Startup performance good${NC}"
    fi
    
    return ${total_time_adb:-$total_time}
}

# Function to run comprehensive performance test suite
run_performance_baseline() {
    echo -e "${BLUE}🚀 Starting Cold Start Performance Baseline Test${NC}"
    echo "Package: $PACKAGE_NAME"
    echo "Date: $(date)"
    echo "Device: $("$ADB_PATH" shell getprop ro.product.model)"
    echo "Android: $("$ADB_PATH" shell getprop ro.build.version.release)"
    echo "=================================="
    
    # Test 1: Cold start to SplashActivity (entry point)
    measure_cold_start "splash_baseline" "com.tqhit.battery.one.activity.splash.SplashActivity" "Splash Activity Cold Start"
    local splash_time=$?

    # Test 2: Cold start to MainActivity (returning user flow)
    measure_cold_start "main_baseline" "com.tqhit.battery.one.activity.main.MainActivity" "Main Activity Cold Start"
    local main_time=$?

    # Test 3: Cold start to StartingActivity (first time user flow)
    measure_cold_start "starting_baseline" "com.tqhit.battery.one.activity.starting.StartingActivity" "Starting Activity Cold Start"
    local starting_time=$?
    
    # Summary report
    echo ""
    echo -e "${BLUE}📊 BASELINE PERFORMANCE SUMMARY${NC}"
    echo "=================================="
    echo "Splash Activity:   ${splash_time}ms"
    echo "Main Activity:     ${main_time}ms"
    echo "Starting Activity: ${starting_time}ms"
    echo ""
    
    # Save baseline to file
    local baseline_file="performance_baseline_$(date +%Y%m%d_%H%M%S).txt"
    {
        echo "TJ_BatteryOne Cold Start Performance Baseline"
        echo "Date: $(date)"
        echo "Device: $("$ADB_PATH" shell getprop ro.product.model)"
        echo "Android: $("$ADB_PATH" shell getprop ro.build.version.release)"
        echo "Package: $PACKAGE_NAME"
        echo ""
        echo "Results:"
        echo "Splash Activity: ${splash_time}ms"
        echo "Main Activity: ${main_time}ms"
        echo "Starting Activity: ${starting_time}ms"
    } > "$baseline_file"
    
    echo -e "${GREEN}💾 Baseline saved to: $baseline_file${NC}"
    
    # Performance recommendations
    echo ""
    echo -e "${BLUE}🎯 OPTIMIZATION RECOMMENDATIONS${NC}"
    if [ $main_time -gt 800 ]; then
        echo -e "${RED}🔴 HIGH PRIORITY: MainActivity startup > 800ms${NC}"
        echo "   - Implement service startup optimization"
        echo "   - Add fragment preloading"
    fi
    
    if [ $splash_time -gt 300 ]; then
        echo -e "${YELLOW}🟡 MEDIUM PRIORITY: SplashActivity > 300ms${NC}"
        echo "   - Add async preloading during splash"
        echo "   - Optimize device adjustments"
    fi
    
    if [ $starting_time -gt 500 ]; then
        echo -e "${YELLOW}🟡 MEDIUM PRIORITY: StartingActivity > 500ms${NC}"
        echo "   - Implement background ad loading"
        echo "   - Optimize onboarding flow"
    fi
}

# Function to monitor real-time startup logs
monitor_startup_logs() {
    local component="${1:-STARTUP_TIMING}"
    echo -e "${BLUE}🔍 Monitoring startup logs for: $component${NC}"
    echo "Press Ctrl+C to stop monitoring"
    
    "$ADB_PATH" shell am force-stop "$PACKAGE_NAME"
    sleep 2
    "$ADB_PATH" logcat -c
    
    echo "🚀 Starting app..."
    "$ADB_PATH" shell am start -n "$PACKAGE_NAME/com.tqhit.battery.one.activity.splash.SplashActivity" &
    
    # Monitor logs in real-time
    "$ADB_PATH" logcat | grep -E "$component" --line-buffered
}

# Function to test specific activity startup
test_activity_startup() {
    local activity="$1"
    local name="$2"
    
    if [ -z "$activity" ]; then
        echo -e "${RED}❌ Activity name required${NC}"
        echo "Usage: $0 test <activity_name> [description]"
        echo "Example: $0 test .activity.main.MainActivity \"Main Activity\""
        exit 1
    fi
    
    measure_cold_start "single_test" "$activity" "${name:-$activity}"
}

# Function to compare performance with baseline
compare_with_baseline() {
    local baseline_file="$1"
    
    if [ ! -f "$baseline_file" ]; then
        echo -e "${RED}❌ Baseline file not found: $baseline_file${NC}"
        echo "Run baseline test first: $0 baseline"
        exit 1
    fi
    
    echo -e "${BLUE}📊 Comparing with baseline: $baseline_file${NC}"
    
    # Run current tests
    run_performance_baseline
    
    # TODO: Add comparison logic here
    echo -e "${YELLOW}⚠️  Comparison feature coming soon${NC}"
}

# Function to show usage
show_usage() {
    echo "TJ_BatteryOne Cold Start Performance Testing"
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  baseline              Run complete baseline performance test"
    echo "  monitor [component]   Monitor real-time startup logs"
    echo "  test <activity> [name] Test specific activity startup"
    echo "  compare <baseline>    Compare current performance with baseline"
    echo "  help                  Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 baseline"
    echo "  $0 monitor STARTUP_TIMING"
    echo "  $0 test .activity.main.MainActivity"
    echo "  $0 compare performance_baseline_20250627_143022.txt"
}

# Main execution
main() {
    case "${1:-baseline}" in
        "baseline")
            check_adb_setup
            run_performance_baseline
            ;;
        "monitor")
            check_adb_setup
            monitor_startup_logs "${2:-STARTUP_TIMING}"
            ;;
        "test")
            check_adb_setup
            test_activity_startup "$2" "$3"
            ;;
        "compare")
            check_adb_setup
            compare_with_baseline "$2"
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            echo -e "${RED}❌ Unknown command: $1${NC}"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
