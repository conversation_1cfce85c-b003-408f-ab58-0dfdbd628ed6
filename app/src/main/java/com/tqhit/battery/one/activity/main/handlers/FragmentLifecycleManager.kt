package com.tqhit.battery.one.activity.main.handlers

import android.util.Log
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.navigation.DynamicNavigationManager
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment
import com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment
import com.tqhit.battery.one.utils.FragmentPreloadCache
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages fragment lifecycle and initialization for MainActivity.
 * Extracted from MainActivity to improve maintainability and separation of concerns.
 *
 * NOTE: This class is a candidate for future refactoring. With the introduction of
 * centralized AppNavigator and SharedNavigationViewModel, much of this functionality
 * could be absorbed into AppNavigator for better consolidation.
 *
 * Responsibilities:
 * - Initial fragment setup based on battery state
 * - Fragment lifecycle coordination with DynamicNavigationManager
 * - Legacy fragment setup as fallback
 * - Fragment state optimization and performance monitoring
 *
 * Following stats module architecture pattern with proper dependency injection.
 */
@Singleton
class FragmentLifecycleManager @Inject constructor(
    private val dynamicNavigationManager: DynamicNavigationManager,
    private val coreBatteryStatsProvider: CoreBatteryStatsProvider
) {
    companion object {
        private const val TAG = "FragmentLifecycleManager"
    }

    /**
     * Sets up the initial fragment based on charging state with optimization.
     * Uses DynamicNavigationManager for modern navigation or falls back to legacy setup.
     * 
     * @param fragmentManager The FragmentManager for fragment transactions
     * @param fragmentContainerId The container ID for fragment replacement
     * @param lifecycleOwner The lifecycle owner for coroutine scope
     * @param navigationHandler The navigation handler for state management
     * @param startTime The startup time for performance monitoring
     */
    fun setupInitialFragmentOptimized(
        fragmentManager: FragmentManager,
        fragmentContainerId: Int,
        lifecycleOwner: LifecycleOwner,
        navigationHandler: NavigationHandler,
        startTime: Long
    ) {
        Log.d(TAG, "STARTUP_TIMING: setupInitialFragmentOptimized() started")

        // Prevent multiple simultaneous setups
        if (navigationHandler.isFragmentSetupInProgress()) {
            Log.w(TAG, "Fragment setup already in progress, skipping")
            return
        }

        navigationHandler.setFragmentSetupInProgress(true)

        // PHASE_1_OPTIMIZATION: Use immediate fragment setup with preloaded fragments
        try {
            val fragmentSetupStartTime = System.currentTimeMillis()

            // Check if we have preloaded fragments available
            if (FragmentPreloadCache.isCacheReady()) {
                Log.d(TAG, "STARTUP_TIMING: Using preloaded fragments for instant setup")
                setupWithPreloadedFragments(fragmentManager, fragmentContainerId, navigationHandler, startTime)
            } else {
                Log.w(TAG, "STARTUP_TIMING: Preloaded fragments not ready, falling back to optimized legacy setup")
                setupOptimizedLegacyFragment(fragmentManager, fragmentContainerId, navigationHandler, startTime)
            }

            Log.d(TAG, "STARTUP_TIMING: Fragment setup completed in ${System.currentTimeMillis() - fragmentSetupStartTime}ms")

        } catch (e: Exception) {
            Log.e(TAG, "Error in optimized fragment setup", e)
            // Final fallback to simple fragment creation
            setupOptimizedLegacyFragment(fragmentManager, fragmentContainerId, navigationHandler, startTime)
        } finally {
            navigationHandler.setFragmentSetupInProgress(false)
        }
    }

    /**
     * REGRESSION_FIX: Setup using preloaded fragments with safe fallback
     */
    private fun setupWithPreloadedFragments(
        fragmentManager: FragmentManager,
        fragmentContainerId: Int,
        navigationHandler: NavigationHandler,
        startTime: Long
    ) {
        val preloadedFragment = FragmentPreloadCache.getAnimationFragment()

        if (preloadedFragment != null) {
            Log.d(TAG, "STARTUP_TIMING: Using preloaded AnimationGridFragment")

            // REGRESSION_FIX: Use non-blocking transaction with proper error handling
            try {
                fragmentManager
                    .beginTransaction()
                    .replace(fragmentContainerId, preloadedFragment)
                    .commitAllowingStateLoss() // Non-blocking, allows state loss for performance

                navigationHandler.setInitialFragmentSet(true)

                val totalTime = System.currentTimeMillis() - startTime
                Log.d(TAG, "STARTUP_TIMING: Preloaded fragment setup completed in ${totalTime}ms")

                // Log cache performance
                FragmentPreloadCache.logCachePerformance()
            } catch (e: Exception) {
                Log.e(TAG, "REGRESSION_FIX: Error using preloaded fragment, falling back", e)
                setupOptimizedLegacyFragment(fragmentManager, fragmentContainerId, navigationHandler, startTime)
            }
        } else {
            Log.d(TAG, "STARTUP_TIMING: Preloaded fragment not available (safe mode), using optimized fallback")
            setupOptimizedLegacyFragment(fragmentManager, fragmentContainerId, navigationHandler, startTime)
        }
    }

    /**
     * REGRESSION_FIX: Optimized legacy fragment setup with error handling
     */
    private fun setupOptimizedLegacyFragment(
        fragmentManager: FragmentManager,
        fragmentContainerId: Int,
        navigationHandler: NavigationHandler,
        startTime: Long
    ) {
        Log.d(TAG, "STARTUP_TIMING: Using optimized legacy fragment setup")

        try {
            // Create fragment directly on main thread (faster than coroutine overhead)
            val initialFragment = AnimationGridFragment()

            // REGRESSION_FIX: Use non-blocking transaction with error handling
            fragmentManager
                .beginTransaction()
                .replace(fragmentContainerId, initialFragment)
                .commitAllowingStateLoss() // Non-blocking for performance

            navigationHandler.setInitialFragmentSet(true)

            val totalTime = System.currentTimeMillis() - startTime
            Log.d(TAG, "STARTUP_TIMING: Optimized legacy fragment setup completed in ${totalTime}ms")

        } catch (e: Exception) {
            Log.e(TAG, "REGRESSION_FIX: Error in optimized legacy fragment setup", e)
            // Final fallback - let the system handle fragment creation
            navigationHandler.setInitialFragmentSet(false)
        }
    }

    /**
     * Legacy fragment setup as fallback when DynamicNavigationManager is not available.
     * 
     * @param fragmentManager The FragmentManager for fragment transactions
     * @param fragmentContainerId The container ID for fragment replacement
     * @param lifecycleOwner The lifecycle owner for coroutine scope
     * @param navigationHandler The navigation handler for state management
     * @param startTime The startup time for performance monitoring
     */
    private fun setupLegacyInitialFragment(
        fragmentManager: FragmentManager,
        fragmentContainerId: Int,
        lifecycleOwner: LifecycleOwner,
        navigationHandler: NavigationHandler,
        startTime: Long
    ) {
        lifecycleOwner.lifecycleScope.launch {
            try {
                val batteryStatusStartTime = System.currentTimeMillis()

                // Get battery status safely with timeout
                val initialBatteryStatus = getBatteryStatusSafely()
                val isCharging = initialBatteryStatus?.isCharging ?: false

                Log.d(TAG, "STARTUP_TIMING: Battery status retrieval took ${System.currentTimeMillis() - batteryStatusStartTime}ms")

                // Switch back to main thread for UI operations
                withContext(Dispatchers.Main) {
                    if (!navigationHandler.isInitialFragmentSet()) {
                        Log.d(TAG, "Legacy initial fragment setup - charging state: $isCharging")
                        val fragmentStartTime = System.currentTimeMillis()

                        // For 2-button navigation, always start with Animation fragment
                        // The Others fragment will provide access to charge/discharge features
                        val initialFragment = AnimationGridFragment()
                        val initialItemId = R.id.animationGridFragment

                        // PHASE_1_OPTIMIZATION: Use non-blocking transaction for performance
                        fragmentManager
                            .beginTransaction()
                            .replace(fragmentContainerId, initialFragment)
                            .commitAllowingStateLoss() // Non-blocking for performance

                        navigationHandler.setInitialFragmentSet(true)

                        val fragmentTime = System.currentTimeMillis() - fragmentStartTime
                        val totalTime = System.currentTimeMillis() - startTime
                        Log.d(TAG, "STARTUP_TIMING: Legacy fragment setup completed in ${fragmentTime}ms (total: ${totalTime}ms)")
                        Log.d(TAG, "Legacy initial fragment set to: ${initialFragment.javaClass.simpleName}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in legacy fragment setup", e)
                // Final fallback - set Animation fragment on main thread
                withContext(Dispatchers.Main) {
                    try {
                        if (!navigationHandler.isInitialFragmentSet()) {
                            val fallbackFragment = AnimationGridFragment()
                            fragmentManager
                                .beginTransaction()
                                .replace(fragmentContainerId, fallbackFragment)
                                .commitAllowingStateLoss() // Non-blocking for performance
                            navigationHandler.setInitialFragmentSet(true)
                            Log.d(TAG, "Fallback fragment setup completed with Animation fragment")
                        }
                    } catch (fallbackException: Exception) {
                        Log.e(TAG, "Critical error in fallback fragment setup", fallbackException)
                    }
                }
            }
        }
    }

    /**
     * Safely retrieves battery status with timeout and error handling.
     * 
     * @return CoreBatteryStatus or null if retrieval fails
     */
    private suspend fun getBatteryStatusSafely(): com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus? {
        return try {
            withContext(Dispatchers.IO) {
                withTimeout(500) {
                    coreBatteryStatsProvider.getCurrentStatus()
                        ?: com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus.createDefault()
                }
            }
        } catch (e: Exception) {
            Log.w(TAG, "Error getting battery status from core provider", e)
            // Return default status if timeout or error
            com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus.createDefault()
        }
    }

    /**
     * Validates fragment state and ensures proper initialization.
     * 
     * @param fragmentManager The FragmentManager to check
     * @param navigationHandler The navigation handler for state checking
     */
    fun validateFragmentState(
        fragmentManager: FragmentManager,
        navigationHandler: NavigationHandler
    ) {
        try {
            val fragments = fragmentManager.fragments
            val visibleFragments = fragments.filter { it?.isVisible == true }
            
            Log.d(TAG, "FRAGMENT_VALIDATION: Total fragments: ${fragments.size}, Visible: ${visibleFragments.size}")
            
            if (visibleFragments.isEmpty() && navigationHandler.isInitialFragmentSet()) {
                Log.w(TAG, "FRAGMENT_VALIDATION: No visible fragments but initial fragment should be set")
            }
            
            if (visibleFragments.size > 1) {
                Log.w(TAG, "FRAGMENT_VALIDATION: Multiple visible fragments detected: ${visibleFragments.map { it?.javaClass?.simpleName }}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error validating fragment state", e)
        }
    }

    /**
     * Gets fragment lifecycle statistics for debugging.
     * 
     * @param fragmentManager The FragmentManager to analyze
     * @return String containing fragment statistics
     */
    fun getFragmentStats(fragmentManager: FragmentManager): String {
        return try {
            val fragments = fragmentManager.fragments
            val visibleCount = fragments.count { it?.isVisible == true }
            val addedCount = fragments.count { it?.isAdded == true }
            val hiddenCount = fragments.count { it?.isHidden == true }
            
            "Fragment Stats - Total: ${fragments.size}, Visible: $visibleCount, Added: $addedCount, Hidden: $hiddenCount"
        } catch (e: Exception) {
            Log.e(TAG, "Error getting fragment stats", e)
            "Fragment Stats: Error retrieving stats"
        }
    }
}
