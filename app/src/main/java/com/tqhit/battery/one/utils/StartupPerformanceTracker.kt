package com.tqhit.battery.one.utils

import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap

/**
 * PHASE_2_OPTIMIZATION: Enhanced startup performance tracking
 * 
 * This singleton tracks detailed startup performance metrics across all phases
 * of the application initialization process.
 * 
 * Key features:
 * - Milestone tracking with timestamps
 * - Performance regression detection
 * - Detailed timing analysis
 * - Memory usage monitoring
 * - Async performance reporting
 */
object StartupPerformanceTracker {
    private const val TAG = "StartupPerformanceTracker"
    
    // Performance tracking data
    private val timingMap = ConcurrentHashMap<String, Long>()
    private val milestoneOrder = mutableListOf<String>()
    private var appStartTime: Long = 0
    private var isTrackingEnabled = true
    
    // Performance thresholds (in milliseconds)
    private const val THRESHOLD_APPLICATION_INIT = 200L
    private const val THRESHOLD_THEME_INIT = 20L
    private const val THRESHOLD_FRAGMENT_SETUP = 50L
    private const val THRESHOLD_SERVICE_STARTUP = 100L
    
    // Coroutine scope for async operations
    private val trackerScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    /**
     * Mark the start of app initialization
     */
    fun markAppStart() {
        if (!isTrackingEnabled) return
        
        appStartTime = System.currentTimeMillis()
        timingMap.clear()
        milestoneOrder.clear()
        
        BatteryLogger.d(TAG, "COLD_START: App process started at $appStartTime")
        markMilestone("app_start")
    }
    
    /**
     * Mark a performance milestone
     */
    fun markMilestone(milestone: String) {
        if (!isTrackingEnabled) return
        
        val currentTime = System.currentTimeMillis()
        val elapsedTime = if (appStartTime > 0) currentTime - appStartTime else 0
        
        timingMap[milestone] = elapsedTime
        milestoneOrder.add(milestone)
        
        BatteryLogger.d(TAG, "COLD_START_MILESTONE: $milestone at ${elapsedTime}ms")
        
        // Check for performance regressions
        checkPerformanceThreshold(milestone, elapsedTime)
    }
    
    /**
     * Mark a milestone with custom timing
     */
    fun markMilestoneWithDuration(milestone: String, durationMs: Long) {
        if (!isTrackingEnabled) return
        
        val currentTime = System.currentTimeMillis()
        val elapsedTime = if (appStartTime > 0) currentTime - appStartTime else 0
        
        timingMap[milestone] = elapsedTime
        milestoneOrder.add(milestone)
        
        BatteryLogger.d(TAG, "COLD_START_MILESTONE: $milestone completed in ${durationMs}ms (total: ${elapsedTime}ms)")
        
        // Check duration-specific thresholds
        checkDurationThreshold(milestone, durationMs)
    }
    
    /**
     * Check if a milestone exceeds performance thresholds
     */
    private fun checkPerformanceThreshold(milestone: String, elapsedTime: Long) {
        val threshold = when {
            milestone.contains("application") -> THRESHOLD_APPLICATION_INIT
            milestone.contains("theme") -> THRESHOLD_THEME_INIT
            milestone.contains("fragment") -> THRESHOLD_FRAGMENT_SETUP
            milestone.contains("service") -> THRESHOLD_SERVICE_STARTUP
            else -> return
        }
        
        if (elapsedTime > threshold) {
            BatteryLogger.w(TAG, "PERFORMANCE_WARNING: $milestone exceeded threshold (${elapsedTime}ms > ${threshold}ms)")
        }
    }
    
    /**
     * Check if a duration exceeds performance thresholds
     */
    private fun checkDurationThreshold(milestone: String, durationMs: Long) {
        val threshold = when {
            milestone.contains("theme") -> THRESHOLD_THEME_INIT
            milestone.contains("fragment") -> THRESHOLD_FRAGMENT_SETUP
            milestone.contains("service") -> THRESHOLD_SERVICE_STARTUP
            else -> return
        }
        
        if (durationMs > threshold) {
            BatteryLogger.w(TAG, "PERFORMANCE_WARNING: $milestone duration exceeded threshold (${durationMs}ms > ${threshold}ms)")
        } else {
            BatteryLogger.d(TAG, "PERFORMANCE_SUCCESS: $milestone within threshold (${durationMs}ms <= ${threshold}ms)")
        }
    }
    
    /**
     * Log comprehensive performance report
     */
    fun logFinalReport() {
        if (!isTrackingEnabled || appStartTime == 0L) return
        
        trackerScope.launch {
            try {
                val totalTime = System.currentTimeMillis() - appStartTime
                
                BatteryLogger.d(TAG, "COLD_START_COMPLETE: Total startup time: ${totalTime}ms")
                BatteryLogger.d(TAG, "COLD_START_SUMMARY: ===== PERFORMANCE REPORT =====")
                
                // Log milestone summary
                milestoneOrder.forEach { milestone ->
                    val time = timingMap[milestone] ?: 0
                    BatteryLogger.d(TAG, "COLD_START_SUMMARY: $milestone: ${time}ms")
                }
                
                // Performance analysis
                analyzePerformance(totalTime)
                
                BatteryLogger.d(TAG, "COLD_START_SUMMARY: ===== END REPORT =====")
                
            } catch (e: Exception) {
                Log.e(TAG, "Error generating performance report", e)
            }
        }
    }
    
    /**
     * Analyze overall performance and provide recommendations
     */
    private fun analyzePerformance(totalTime: Long) {
        BatteryLogger.d(TAG, "PERFORMANCE_ANALYSIS: Total startup time: ${totalTime}ms")
        
        when {
            totalTime < 1000 -> {
                BatteryLogger.d(TAG, "PERFORMANCE_ANALYSIS: ✅ Excellent startup performance (<1s)")
            }
            totalTime < 2000 -> {
                BatteryLogger.d(TAG, "PERFORMANCE_ANALYSIS: ✅ Good startup performance (<2s)")
            }
            totalTime < 5000 -> {
                BatteryLogger.w(TAG, "PERFORMANCE_ANALYSIS: ⚠️ Acceptable startup performance (<5s)")
            }
            else -> {
                BatteryLogger.w(TAG, "PERFORMANCE_ANALYSIS: ❌ Poor startup performance (>5s) - optimization needed")
            }
        }
        
        // Identify bottlenecks
        identifyBottlenecks()
    }
    
    /**
     * Identify performance bottlenecks from timing data
     */
    private fun identifyBottlenecks() {
        val sortedTimings = timingMap.toList().sortedByDescending { (_, time) -> time }
        
        BatteryLogger.d(TAG, "PERFORMANCE_BOTTLENECKS: Top performance bottlenecks:")
        sortedTimings.take(5).forEach { (milestone, time) ->
            BatteryLogger.d(TAG, "PERFORMANCE_BOTTLENECKS: $milestone: ${time}ms")
        }
    }
    
    /**
     * Get performance statistics as a formatted string
     */
    fun getPerformanceStats(): String {
        if (appStartTime == 0L) return "No performance data available"
        
        val totalTime = System.currentTimeMillis() - appStartTime
        val milestoneCount = timingMap.size

        return buildString {
            appendLine("Performance Stats:")
            appendLine("Total Time: ${totalTime}ms")
            appendLine("Milestones: $milestoneCount")
            appendLine("Average per milestone: ${if (milestoneCount > 0) totalTime / milestoneCount else 0L}ms")
        }
    }
    
    /**
     * Reset tracking data
     */
    fun reset() {
        timingMap.clear()
        milestoneOrder.clear()
        appStartTime = 0L
        BatteryLogger.d(TAG, "Performance tracking reset")
    }
    
    /**
     * Enable/disable performance tracking
     */
    fun setTrackingEnabled(enabled: Boolean) {
        isTrackingEnabled = enabled
        BatteryLogger.d(TAG, "Performance tracking ${if (enabled) "enabled" else "disabled"}")
    }
    
    /**
     * Check if tracking is enabled
     */
    fun isTrackingEnabled(): Boolean = isTrackingEnabled
}
